---
# yaml-language-server: $schema=https://json.schemastore.org/helmfile

helmDefaults:
  cleanupOnFail: true
  wait: true
  waitForJobs: true

repositories:
  - name: cilium
    url: https://helm.cilium.io

releases:
  - name: cilium
    namespace: kube-system
    atomic: true
    chart: cilium/cilium
    version: 1.17.5
    values: ['../kubernetes/apps/kube-system/cilium/app/helm/values.yaml']

  - name: coredns
    namespace: kube-system
    atomic: true
    chart: oci://ghcr.io/coredns/charts/coredns
    version: 1.43.0
    values: ['../kubernetes/apps/kube-system/coredns/app/helm/values.yaml']
    needs: ['kube-system/cilium']

  - name: spegel
    namespace: kube-system
    atomic: true
    chart: oci://ghcr.io/spegel-org/helm-charts/spegel
    version: 0.3.0
    values: ['../kubernetes/apps/kube-system/spegel/app/helm/values.yaml']
    needs: ['kube-system/coredns']

  - name: cert-manager
    namespace: cert-manager
    atomic: true
    chart: oci://quay.io/jetstack/charts/cert-manager
    version: v1.17.2
    values: ['../kubernetes/apps/cert-manager/cert-manager/app/helm/values.yaml']
    needs: ['kube-system/spegel']

  - name: flux-operator
    namespace: flux-system
    atomic: true
    chart: oci://ghcr.io/controlplaneio-fluxcd/charts/flux-operator
    version: 0.23.0
    values: ['../kubernetes/apps/flux-system/flux-operator/app/helm/values.yaml']
    needs: ['cert-manager/cert-manager']

  - name: flux-instance
    namespace: flux-system
    atomic: true
    chart: oci://ghcr.io/controlplaneio-fluxcd/charts/flux-instance
    version: 0.23.0
    values: ['../kubernetes/apps/flux-system/flux-instance/app/helm/values.yaml']
    needs: ['flux-system/flux-operator']
