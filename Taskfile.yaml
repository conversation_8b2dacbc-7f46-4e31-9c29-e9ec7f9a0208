---
# yaml-language-server: $schema=https://taskfile.dev/schema.json
version: '3'

set: [pipefail]
shopt: [globstar]

vars:
  BOOTSTRAP_DIR: '{{.ROOT_DIR}}/bootstrap'
  KUBERNETES_DIR: '{{.ROOT_DIR}}/kubernetes'
  SCRIPTS_DIR: '{{.ROOT_DIR}}/scripts'
  TALOS_DIR: '{{.ROOT_DIR}}/talos'
  PRIVATE_DIR: '{{.ROOT_DIR}}/.private'
  TALOSCONFIG: '{{.ROOT_DIR}}/talos/clusterconfig/talosconfig'

env:
  KUBECONFIG: '{{.ROOT_DIR}}/kubeconfig'
  SOPS_AGE_KEY_FILE: '{{.ROOT_DIR}}/age.key'
  TALOSCONFIG: '{{.TALOSCONFIG}}'

includes:
  bootstrap: .taskfiles/bootstrap
  talos: .taskfiles/talos
  template: .taskfiles/template
  unraid: .taskfiles/unraid

tasks:

  default: task --list

  reconcile:
    desc: Force Flux to pull in changes from your Git repository
    cmd: flux --namespace flux-system reconcile kustomization flux-system --with-source
    preconditions:
      - test -f {{.KUBECONFIG}}
      - which flux

  provision:
    desc: Provision VMs on Unraid and bootstrap Talos cluster
    cmds:
      - task: unraid:provision
