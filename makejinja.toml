[makejinja]
inputs = ["./templates/overrides","./templates/config"]
output = "./"
exclude_patterns = ["*.partial.yaml.j2"]
data = ["./cluster.yaml", "./nodes.yaml"]
import_paths = ["./templates/scripts"]
loaders = ["plugin:Plugin"]
jinja_suffix = ".j2"
copy_metadata = true
force = true
undefined = "chainable"

[makejinja.delimiter]
block_start = "#%"
block_end = "%#"
comment_start = "#|"
comment_end = "#|"
variable_start = "#{"
variable_end = "}#"
