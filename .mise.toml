[env]
_.python.venv = { path = "{{config_root}}/.venv", create = true }
KUBECONFIG = "{{config_root}}/kubeconfig"
SOPS_AGE_KEY_FILE = "{{config_root}}/age.key"
TALOSCONFIG = "{{config_root}}/talos/clusterconfig/talosconfig"

[tools]
"python" = "3.13"
"pipx:makejinja" = "2.8.0"
"aqua:budimanjojo/talhelper" = "3.0.30"
"aqua:cilium/cilium-cli" = "0.18.5"
"aqua:cli/cli" = "2.74.2"
"aqua:cloudflare/cloudflared" = "2025.7.0"
"aqua:cue-lang/cue" = "0.13.2"
"aqua:FiloSottile/age" = "1.2.1"
"aqua:fluxcd/flux2" = "2.6.4"
"aqua:getsops/sops" = "3.10.2"
"aqua:go-task/task" = "3.44.0"
"aqua:helm/helm" = "3.18.4"
"aqua:helmfile/helmfile" = "1.1.2"
"aqua:jqlang/jq" = "1.8.1"
"aqua:kubernetes-sigs/kustomize" = "5.7.0"
"aqua:kubernetes/kubectl" = "1.33.2"
"aqua:mikefarah/yq" = "4.45.4"
"aqua:siderolabs/talos" = "1.10.5"
"aqua:yannh/kubeconform" = "0.7.0"
