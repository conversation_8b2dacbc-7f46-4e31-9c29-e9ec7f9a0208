---
# yaml-language-server: $schema=https://json.schemastore.org/github-workflow.json
name: "Labeler"

on:
  workflow_dispatch:
  pull_request_target:
    branches: ["main"]

jobs:
  labeler:
    name: Labeler
    runs-on: ubuntu-latest
    permissions:
      contents: read
      pull-requests: write
    steps:
      - name: Labeler
        uses: actions/labeler@8558fd74291d67161a8a78ce36a881fa63b766a9 # v5.0.0
        with:
          configuration-path: .github/labeler.yaml
