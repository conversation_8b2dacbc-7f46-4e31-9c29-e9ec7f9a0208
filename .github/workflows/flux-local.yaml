---
# yaml-language-server: $schema=https://json.schemastore.org/github-workflow.json
name: "Flux Local"

on:
  pull_request:
    branches: ["main"]

concurrency:
  group: ${{ github.workflow }}-${{ github.event.number || github.ref }}
  cancel-in-progress: true

jobs:
  pre-job:
    name: Flux Local Pre-Job
    runs-on: ubuntu-latest
    outputs:
      any_changed: ${{ steps.changed-files.outputs.any_changed }}
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Get Changed Files
        id: changed-files
        uses: tj-actions/changed-files@ed68ef82c095e0d48ec87eccea555d944a631a4c # v46.0.5
        with:
          files: kubernetes/**

  test:
    name: Flux Local Test
    needs: pre-job
    runs-on: ubuntu-latest
    if: ${{ needs.pre-job.outputs.any_changed == 'true' }}
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Run flux-local test
        uses: docker://ghcr.io/allenporter/flux-local:v7.5.6
        with:
          args: test --enable-helm --all-namespaces --path /github/workspace/kubernetes/flux/cluster -v

  diff:
    name: Flux Local Diff
    needs: pre-job
    runs-on: ubuntu-latest
    permissions:
      contents: read
      pull-requests: write
    strategy:
      matrix:
        resources: ["helmrelease", "kustomization"]
      max-parallel: 4
      fail-fast: false
    if: ${{ needs.pre-job.outputs.any_changed == 'true' }}
    steps:
      - name: Checkout Pull Request Branch
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          path: pull

      - name: Checkout Default Branch
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          ref: "${{ github.event.repository.default_branch }}"
          path: default

      - name: Run flux-local diff
        uses: docker://ghcr.io/allenporter/flux-local:v7.5.6
        with:
          args: >-
            diff ${{ matrix.resources }}
            --unified 6
            --path /github/workspace/pull/kubernetes/flux/cluster
            --path-orig /github/workspace/default/kubernetes/flux/cluster
            --strip-attrs "helm.sh/chart,checksum/config,app.kubernetes.io/version,chart"
            --limit-bytes 10000
            --all-namespaces
            --sources "flux-system"
            --output-file diff.patch

      - name: Generate Diff
        id: diff
        run: |
          cat diff.patch;
          {
              echo 'diff<<EOF'
              cat diff.patch
              echo EOF
          } >> "$GITHUB_OUTPUT";
          {
              echo "### Diff"
              echo '```diff'
              cat diff.patch
              echo '```'
          } >> "$GITHUB_STEP_SUMMARY"

      - name: Add Comment
        if: ${{ steps.diff.outputs.diff != '' }}
        continue-on-error: true
        uses: mshick/add-pr-comment@b8f338c590a895d50bcbfa6c5859251edc8952fc # v2.8.2
        with:
          message-id: "${{ github.event.pull_request.number }}/kubernetes/${{ matrix.resources }}"
          message-failure: Diff was not successful
          message: |
            ```diff
            ${{ steps.diff.outputs.diff }}
            ```

  flux-local-status:
    name: Flux Local Success
    needs: ["test", "diff"]
    runs-on: ubuntu-latest
    if: ${{ always() }}
    steps:
      - name: Any jobs failed?
        if: ${{ contains(needs.*.result, 'failure') }}
        run: exit 1

      - name: All jobs passed or skipped?
        if: ${{ !(contains(needs.*.result, 'failure')) }}
        run: echo "All jobs passed or skipped" && echo "${{ toJSON(needs.*.result) }}"
