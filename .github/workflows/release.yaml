---
# yaml-language-server: $schema=https://json.schemastore.org/github-workflow.json
name: "Release"

on:
  workflow_dispatch:
  schedule:
    - cron: "0 0 1 * *" # 1st of every month at midnight

jobs:
  release:
    name: Release
    runs-on: ubuntu-latest
    steps:
      - name: Get Previous Release Tag and Determine Next Tag
        id: determine-next-tag
        uses: actions/github-script@60a0d83039c74a4aee543508d2ffcb1c3799cdea # v7.0.1
        with:
          github-token: "${{ secrets.GITHUB_TOKEN }}"
          result-encoding: string
          script: |
            const { data: releases } = await github.rest.repos.listReleases({
              owner: context.repo.owner,
              repo: context.repo.repo,
              per_page: 1,
            });

            let previousTag = "0.0.0"; // Default if no previous release exists
            if (releases.length > 0) {
              previousTag = releases[0].tag_name;
            }

            const [previousMajor, previousMinor, previousPatch] = previousTag.split('.').map(Number);
            const currentYear = new Date().getFullYear();
            const currentMonth = new Date().getMonth() + 1; // Months are 0-indexed in JavaScript

            const nextMajorMinor = `${currentYear}.${currentMonth}`;
            let nextPatch;

            if (`${previousMajor}.${previousMinor}` === nextMajorMinor) {
              console.log("Month release already exists for the year. Incrementing patch number by 1.");
              nextPatch = previousPatch + 1;
            } else {
              console.log("Month release does not exist for the year. Starting with patch number 0.");
              nextPatch = 0;
            }

            return `${nextMajorMinor}.${nextPatch}`;

      - name: Create Release
        uses: ncipollo/release-action@440c8c1cb0ed28b9f43e4d1d670870f059653174 # v1.16.0
        with:
          generateReleaseNotes: true
          tag: "${{ steps.determine-next-tag.outputs.result }}"
          token: "${{ secrets.GITHUB_TOKEN }}"
