---
# yaml-language-server: $schema=https://raw.githubusercontent.com/fluxcd-community/flux2-schemas/main/kustomization-kustomize-v1.json
apiVersion: kustomize.toolkit.fluxcd.io/v1
kind: Kustomization
metadata:
  name: cluster-meta
  namespace: &namespace flux-system
spec:
  decryption:
    provider: sops
    secretRef:
      name: sops-age
  interval: 1h
  path: ./kubernetes/flux/meta
  prune: true
  retryInterval: 2m
  sourceRef:
    kind: GitRepository
    name: flux-system
    namespace: flux-system
  # Flux repositories under this need flux-system hardcoded as namespace for Renovate lookups
  targetNamespace: *namespace
  wait: true
---
# yaml-language-server: $schema=https://raw.githubusercontent.com/fluxcd-community/flux2-schemas/main/kustomization-kustomize-v1.json
apiVersion: kustomize.toolkit.fluxcd.io/v1
kind: Kustomization
metadata:
  name: cluster-apps
  namespace: flux-system
spec:
  decryption:
    provider: sops
    secretRef:
      name: sops-age
  dependsOn:
    - name: cluster-meta
      namespace: flux-system
  interval: 1h
  path: ./kubernetes/apps
  prune: true
  retryInterval: 2m
  sourceRef:
    kind: GitRepository
    name: flux-system
    namespace: flux-system
  timeout: 5m
  wait: false
