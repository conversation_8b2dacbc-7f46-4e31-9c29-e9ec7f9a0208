---
# yaml-language-server: $schema=https://raw.githubusercontent.com/fluxcd-community/flux2-schemas/main/helmrelease-helm-v2.json
apiVersion: helm.toolkit.fluxcd.io/v2
kind: HelmRelease
metadata:
  name: echo
spec:
  interval: 1h
  chartRef:
    kind: OCIRepository
    name: app-template
  install:
    remediation:
      retries: -1
  upgrade:
    cleanupOnFail: true
    remediation:
      retries: 3
  dependsOn:
    - name: cloudflare-tunnel
      namespace: network
  values:
    controllers:
      echo:
        strategy: RollingUpdate
        containers:
          app:
            image:
              repository: ghcr.io/mendhak/http-https-echo
              tag: 37
            env:
              HTTP_PORT: &port 80
              LOG_WITHOUT_NEWLINE: true
              LOG_IGNORE_PATH: /healthz
              PROMETHEUS_ENABLED: true
            probes:
              liveness: &probes
                enabled: true
                custom: true
                spec:
                  httpGet:
                    path: /healthz
                    port: *port
                  initialDelaySeconds: 0
                  periodSeconds: 10
                  timeoutSeconds: 1
                  failureThreshold: 3
              readiness: *probes
            securityContext:
              allowPrivilegeEscalation: false
              readOnlyRootFilesystem: true
              capabilities: { drop: ["ALL"] }
            resources:
              requests:
                cpu: 10m
              limits:
                memory: 64Mi
    defaultPodOptions:
      securityContext:
        runAsNonRoot: true
        runAsUser: 65534
        runAsGroup: 65534
    service:
      app:
        ports:
          http:
            port: *port
    serviceMonitor:
      app:
        endpoints:
          - port: http
    route:
      app:
        hostnames: ["{{ .Release.Name }}.${SECRET_DOMAIN}"]
        parentRefs:
          - name: external
            namespace: kube-system
            sectionName: https
        rules:
          - backendRefs:
              - identifier: app
                port: *port
