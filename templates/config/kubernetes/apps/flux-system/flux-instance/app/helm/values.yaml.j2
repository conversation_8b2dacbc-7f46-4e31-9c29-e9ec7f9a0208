---
instance:
  distribution:
    # renovate: datasource=github-releases depName=controlplaneio-fluxcd/distribution
    version: 2.6.2
  cluster:
    networkPolicy: false
  components:
    - source-controller
    - kustomize-controller
    - helm-controller
    - notification-controller
  sync:
    kind: GitRepository
    #% if repository_visibility == 'private' %#
    url: "ssh://**************/#{ repository_name }#.git"
    pullSecret: github-deploy-key
    #% else %#
    url: "https://github.com/#{ repository_name }#.git"
    #% endif %#
    ref: "refs/heads/#{ repository_branch }#"
    path: kubernetes/flux/cluster
  commonMetadata:
    labels:
      app.kubernetes.io/name: flux
  kustomize:
    patches:
      - # Increase the number of workers
        patch: |
          - op: add
            path: /spec/template/spec/containers/0/args/-
            value: --concurrent=10
          - op: add
            path: /spec/template/spec/containers/0/args/-
            value: --requeue-dependency=5s
        target:
          kind: Deployment
          name: (kustomize-controller|helm-controller|source-controller)
      - # Increase the memory limits
        patch: |
          apiVersion: apps/v1
          kind: Deployment
          metadata:
            name: all
          spec:
            template:
              spec:
                containers:
                  - name: manager
                    resources:
                      limits:
                        memory: 1Gi
        target:
          kind: Deployment
          name: (kustomize-controller|helm-controller|source-controller)
      - # Enable in-memory kustomize builds
        patch: |
          - op: add
            path: /spec/template/spec/containers/0/args/-
            value: --concurrent=20
          - op: replace
            path: /spec/template/spec/volumes/0
            value:
              name: temp
              emptyDir:
                medium: Memory
        target:
          kind: Deployment
          name: kustomize-controller
      - # Enable Helm repositories caching
        patch: |
          - op: add
            path: /spec/template/spec/containers/0/args/-
            value: --helm-cache-max-size=10
          - op: add
            path: /spec/template/spec/containers/0/args/-
            value: --helm-cache-ttl=60m
          - op: add
            path: /spec/template/spec/containers/0/args/-
            value: --helm-cache-purge-interval=5m
        target:
          kind: Deployment
          name: source-controller
      - # Flux near OOM detection for Helm
        patch: |
          - op: add
            path: /spec/template/spec/containers/0/args/-
            value: --feature-gates=OOMWatch=true
          - op: add
            path: /spec/template/spec/containers/0/args/-
            value: --oom-watch-memory-threshold=95
          - op: add
            path: /spec/template/spec/containers/0/args/-
            value: --oom-watch-interval=500ms
        target:
          kind: Deployment
          name: helm-controller
      - # Disable chart digest tracking
        patch: |
          - op: add
            path: /spec/template/spec/containers/0/args/-
            value: --feature-gates=DisableChartDigestTracking=true
        target:
          kind: Deployment
          name: helm-controller
