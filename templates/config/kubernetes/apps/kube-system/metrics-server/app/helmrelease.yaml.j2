---
# yaml-language-server: $schema=https://raw.githubusercontent.com/fluxcd-community/flux2-schemas/main/helmrepository-source-v1.json
apiVersion: source.toolkit.fluxcd.io/v1
kind: HelmRepository
metadata:
  name: metrics-server
  namespace: kube-system # Required for Renovate lookups
spec:
  interval: 1h
  url: https://kubernetes-sigs.github.io/metrics-server
---
# yaml-language-server: $schema=https://raw.githubusercontent.com/fluxcd-community/flux2-schemas/main/helmrelease-helm-v2.json
apiVersion: helm.toolkit.fluxcd.io/v2
kind: HelmRelease
metadata:
  name: metrics-server
spec:
  interval: 1h
  chart:
    spec:
      chart: metrics-server
      version: 3.12.2
      sourceRef:
        kind: HelmRepository
        name: metrics-server
        namespace: kube-system
  install:
    remediation:
      retries: -1
  upgrade:
    cleanupOnFail: true
    remediation:
      retries: 3
  values:
    args:
      - --kubelet-insecure-tls
      - --kubelet-preferred-address-types=InternalIP,ExternalIP,Hostname
      - --kubelet-use-node-status-port
      - --metric-resolution=10s
      - --kubelet-request-timeout=2s
    metrics:
      enabled: true
    serviceMonitor:
      enabled: true
