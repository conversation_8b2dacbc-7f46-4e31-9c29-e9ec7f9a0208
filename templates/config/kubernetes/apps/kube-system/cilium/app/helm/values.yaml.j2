---
autoDirectNodeRoutes: true
bpf:
  masquerade: true
  # Ref: https://github.com/siderolabs/talos/issues/10002
  hostLegacyRouting: true
#% if cilium_bgp_enabled %#
bgpControlPlane:
  enabled: true
#% endif %#
cni:
  # Required for pairing with Multus CNI
  exclusive: false
cgroup:
  automount:
    enabled: false
  hostRoot: /sys/fs/cgroup
# NOTE: devices might need to be set if you have more than one active NIC on your hosts
# devices: eno+ eth+
dashboards:
  enabled: true
endpointRoutes:
  enabled: true
envoy:
  rollOutPods: true
  prometheus:
     serviceMonitor:
       enabled: true
gatewayAPI:
  enabled: true
hubble:
  enabled: false
ipam:
  mode: kubernetes
ipv4NativeRoutingCIDR: "#{ cluster_pod_cidr }#"
k8sServiceHost: 127.0.0.1
k8sServicePort: 7445
kubeProxyReplacement: true
kubeProxyReplacementHealthzBindAddr: 0.0.0.0:10256
l2announcements:
  enabled: true
loadBalancer:
  algorithm: maglev
  mode: "#{ cilium_loadbalancer_mode }#"
localRedirectPolicy: true
operator:
  dashboards:
    enabled: true
  prometheus:
    enabled: true
    serviceMonitor:
      enabled: true
  replicas: 1
  rollOutPods: true
prometheus:
  enabled: true
  serviceMonitor:
    enabled: true
    trustCRDsExist: true
rollOutCiliumPods: true
routingMode: native
securityContext:
  capabilities:
    ciliumAgent:
      - CHOWN
      - KILL
      - NET_ADMIN
      - NET_RAW
      - IPC_LOCK
      - SYS_ADMIN
      - SYS_RESOURCE
      - PERFMON
      - BPF
      - DAC_OVERRIDE
      - FOWNER
      - SETGID
      - SETUID
    cleanCiliumState:
      - NET_ADMIN
      - SYS_ADMIN
      - SYS_RESOURCE
socketLB:
  hostNamespaceOnly: true
