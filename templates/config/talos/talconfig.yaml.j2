# yaml-language-server: $schema=https://raw.githubusercontent.com/budimanjojo/talhelper/master/pkg/config/schemas/talconfig.json
---
clusterName: kubernetes

talosVersion: "${talosVersion}"
kubernetesVersion: "${kubernetesVersion}"

endpoint: https://#{ cluster_api_addr }#:6443
additionalApiServerCertSans: &sans
  - "127.0.0.1"
  - "#{ cluster_api_addr }#"
  #% for item in cluster_api_tls_sans %#
  - "#{ item }#"
  #% endfor %#
additionalMachineCertSans: *sans

clusterPodNets: ["#{ cluster_pod_cidr }#"]
clusterSvcNets: ["#{ cluster_svc_cidr }#"]

# Disable built-in CNI to use Cilium
cniConfig:
  name: none

nodes:
  #% for item in nodes %#
  - hostname: "#{ item.name }#"
    ipAddress: "#{ item.address }#"
    #% if item.disk.startswith('/') %#
    installDisk: "#{ item.disk }#"
    #% else %#
    installDiskSelector:
      serial: "#{ item.disk }#"
    #% endif %#
    machineSpec:
      secureboot: #{ (true if item.secureboot else false) | string | lower }#
    talosImageURL: factory.talos.dev/installer#{ "-secureboot" if item.secureboot | default(false, true) }#/#{ item.schematic_id }#
    controlPlane: #{ (item.controller) | string | lower }#
    networkInterfaces:
      - deviceSelector:
          hardwareAddr: "#{ item.mac_addr | lower }#"
        #% if node_vlan_tag %#
        vlans:
          - vlanId: #{ node_vlan_tag }#
            addresses:
              - "#{ item.address }#/#{ node_cidr.split('/') | last }#"
            mtu: #{ item.mtu | default(1500, true) }#
            routes:
              - network: "0.0.0.0/0"
                gateway: "#{ node_default_gateway }#"
            #% if item.controller %#
            vip:
              ip: "#{ cluster_api_addr }#"
            #% endif %#
        #% else %#
        dhcp: false
        addresses:
          - "#{ item.address }#/#{ node_cidr.split('/') | last }#"
        routes:
          - network: "0.0.0.0/0"
            gateway: "#{ node_default_gateway }#"
        mtu: #{ item.mtu | default(1500, true) }#
        #% if item.controller %#
        vip:
          ip: "#{ cluster_api_addr }#"
        #% endif %#
        #% endif %#
    #% if talos_patches('%s' % (item.name)) | length == 0 %#
    #% if item.encrypt_disk | default(false, true) %#
    patches:
      - # Encrypt system disk with TPM
        |-
        machine:
          systemDiskEncryption:
            state:
              provider: luks2
              keys:
                - slot: 0
                  tpm: {}
            ephemeral:
              provider: luks2
              keys:
                - slot: 0
                  tpm: {}
    #% endif %#
    #% else %#
    #% for file in talos_patches('%s' % (item.name)) %#
    #% if loop.index == 1 %#
    patches:
    #% if item.encrypt_disk | default(false, true) %#
      - |-
        machine:
          systemDiskEncryption:
            state:
              provider: luks2
              keys:
                - slot: 0
                  tpm: {}
            ephemeral:
              provider: luks2
              keys:
                - slot: 0
                  tpm: {}
    #% endif %#
    #% endif %#
      - "@./patches/#{ item.name }#/#{ file | basename }#"
    #% endfor %#
    #% endif %#
  #% endfor %#

#% for file in talos_patches('global') %#
#% if loop.index == 1 %#
# Global patches
patches:
#% endif %#
  - "@./patches/global/#{ file | basename }#"
#% endfor %#

#% for file in talos_patches('controller') %#
#% if loop.index == 1 %#
# Controller patches
controlPlane:
  patches:
#% endif %#
    - "@./patches/controller/#{ file | basename }#"
#% endfor %#

#% if (nodes | selectattr('controller', 'equalto', False) | list | length) and (talos_patches('worker') | length) %#
#% for file in talos_patches('worker') %#
#% if loop.index == 1 %#
# Worker patches
worker:
  patches:
#% endif %#
    - "@./patches/worker/#{ file | basename }#"
#% endfor %#
#% endif %#
