# VirtioFS Setup for Unraid Shares

This guide explains how to configure VirtioFS to mount Unraid shares directly into Talos nodes, eliminating the need for distributed storage solutions like Rook Ceph.

## Benefits

- **Simplified Architecture**: No more complex distributed storage
- **Better Performance**: Near-native filesystem performance (600+ MB/s)
- **Resource Efficiency**: Eliminates Ceph OSDs, monitors, and managers
- **Centralized Storage**: Leverage Unraid's array and parity protection
- **Easier Backup**: Use Unraid's built-in backup capabilities

## Prerequisites

- Unraid 6.12+ with libvirt 6.2+
- Talos Linux VMs running on Unraid
- Shared directories created on Unraid

## Step 1: Configure Unraid Shares

1. Create shared directories on Unraid:
   ```bash
   mkdir -p /mnt/flashstor/Talos-Unraid-Share
   chmod 755 /mnt/flashstor/Talos-Unraid-Share
   ```

2. Set appropriate ownership (use UID 1000 for most cases):
   ```bash
   chown -R 1000:1000 /mnt/flashstor/Talos-Unraid-Share
   ```

## Step 2: Configure VirtioFS in VM XML

For each Talos VM, add VirtioFS filesystem entries to the VM XML configuration:

```xml
<domain>
  <!-- Existing configuration -->
  
  <!-- Required for VirtioFS -->
  <memoryBacking>
    <source type='memfd'/>
    <access mode='shared'/>
  </memoryBacking>
  
  <devices>
    <!-- Existing devices -->
    
    <!-- VirtioFS filesystems -->
    <filesystem type='mount' accessmode='passthrough'>
      <driver type='virtiofs' queue='1024'/>
      <source dir='/mnt/flashstor/Talos-Unraid-Share'/>
      <target dir='talos_unraid_share'/>
      <binary path='/usr/libexec/virtiofsd' xattr='on'>
        <cache mode='always'/>
        <lock posix='on' flock='on'/>
      </binary>
    </filesystem>
  </devices>
</domain>
```

## Step 3: Apply Talos Configuration

The VirtioFS mount configuration is already included in this cluster setup:

1. **Machine Patch**: `talos/patches/global/machine-virtiofs.yaml`
   - Mounts VirtioFS share at `/var/mnt/talos-unraid-share`
   - Exposes mount to kubelet for pod access

2. **StorageClass**: `kubernetes/apps/openebs-system/openebs/app/storageclass-virtiofs.yaml`
   - `openebs-virtiofs`: Default StorageClass using `/var/mnt/talos-unraid-share`

3. **Apply the configuration**:
   ```bash
   # Regenerate Talos config
   task talos:generate-config
   
   # Apply to all nodes
   task talos:apply-node IP=*************** MODE=auto
   task talos:apply-node IP=*************** MODE=auto
   task talos:apply-node IP=*************** MODE=auto
   task talos:apply-node IP=*************** MODE=auto
   task talos:apply-node IP=*************** MODE=auto
   ```

## Step 4: Verify VirtioFS Mounts

After applying the configuration:

```bash
# Check mounts on Talos nodes
talosctl get mounts --nodes ***************

# Verify filesystem access
talosctl exec --node *************** -- ls -la /var/mnt/talos-unraid-share
talosctl exec --node *************** -- df -h /var/mnt/talos-unraid-share
```

## Step 5: Migration from Rook Ceph

### Before Migration
1. **Backup all data** from existing PVCs
2. **Scale down applications** using Ceph storage
3. **Export PVC data** if needed

### Migration Steps
1. **Update applications** to use new StorageClass:
   ```yaml
   # Change from:
   storageClassName: ceph-block
   # To:
   storageClassName: openebs-virtiofs
   ```

2. **Remove Rook Ceph** (after data migration):
   ```bash
   # Remove Rook Ceph application
   kubectl delete -k kubernetes/apps/rook-ceph/
   
   # Clean up CRDs and finalizers
   kubectl patch cephcluster -n rook-ceph rook-ceph --type merge -p '{"spec":{"cleanupPolicy":{"confirmation":"yes-really-destroy-data"}}}'
   ```

3. **Update default StorageClass**:
   ```bash
   # Remove default annotation from ceph-block
   kubectl patch storageclass ceph-block -p '{"metadata": {"annotations":{"storageclass.kubernetes.io/is-default-class":"false"}}}'
   
   # openebs-virtiofs is already set as default
   ```

## Step 6: Testing

1. **Create a test PVC**:
   ```yaml
   apiVersion: v1
   kind: PersistentVolumeClaim
   metadata:
     name: test-virtiofs-pvc
   spec:
     accessModes:
       - ReadWriteOnce
     resources:
       requests:
         storage: 1Gi
     storageClassName: openebs-virtiofs
   ```

2. **Create a test pod**:
   ```yaml
   apiVersion: v1
   kind: Pod
   metadata:
     name: test-virtiofs-pod
   spec:
     containers:
     - name: test
       image: busybox
       command: ["sleep", "3600"]
       volumeMounts:
       - name: storage
         mountPath: /data
     volumes:
     - name: storage
       persistentVolumeClaim:
         claimName: test-virtiofs-pvc
   ```

3. **Test read/write performance**:
   ```bash
   kubectl exec test-virtiofs-pod -- dd if=/dev/zero of=/data/test bs=1M count=100
   kubectl exec test-virtiofs-pod -- dd if=/data/test of=/dev/null bs=1M
   ```

## Troubleshooting

### VirtioFS Not Mounting
- Verify `memoryBacking` is configured in VM XML
- Check that `virtiofsd` is available on Unraid
- Ensure target directory names match in VM XML and Talos config

### Permission Issues
- Verify directory ownership on Unraid matches VM user ID
- Check that directories have appropriate permissions (755 or 777)

### Performance Issues
- Ensure `cache mode='always'` is set in VM XML
- Verify sufficient memory is allocated to VMs
- Check Unraid array performance and disk health

## Performance Comparison

| Storage Type | Read Speed | Write Speed | Latency |
|--------------|------------|-------------|----------|
| VirtioFS     | 600+ MB/s  | 400+ MB/s   | <1ms     |
| 9p           | 100 MB/s   | 50 MB/s     | 2-5ms    |
| NFS          | 200 MB/s   | 150 MB/s    | 1-2ms    |
| Rook Ceph    | 300 MB/s   | 200 MB/s    | 1-3ms    |

*Performance may vary based on hardware and configuration*