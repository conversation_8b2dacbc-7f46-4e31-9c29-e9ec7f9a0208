---
# yaml-language-server: $schema=https://raw.githubusercontent.com/fluxcd-community/flux2-schemas/main/helmrelease-helm-v2.json
apiVersion: helm.toolkit.fluxcd.io/v2
kind: HelmRelease
metadata:
  name: &app cloudflare-dns
spec:
  interval: 1h
  chart:
    spec:
      chart: external-dns
      version: 1.17.0
      sourceRef:
        kind: HelmRepository
        name: external-dns
        namespace: flux-system
  install:
    remediation:
      retries: -1
  upgrade:
    cleanupOnFail: true
    remediation:
      strategy: rollback
      retries: 3
  values:
    fullnameOverride: *app
    provider: cloudflare
    env:
      - name: CF_API_TOKEN
        valueFrom:
          secretKeyRef:
            name: &secret cloudflare-dns-secret
            key: api-token
    extraArgs:
      - --cloudflare-dns-records-per-page=1000
      - --cloudflare-proxied
      - --crd-source-apiversion=externaldns.k8s.io/v1alpha1
      - --crd-source-kind=DNSEndpoint
      - --gateway-name=external
    triggerLoopOnEvent: true
    policy: sync
    sources: ["crd", "gateway-httproute"]
    txtPrefix: k8s.
    txtOwnerId: default
    domainFilters: ["${SECRET_DOMAIN}"]
    serviceMonitor:
      enabled: true
    podAnnotations:
      secret.reloader.stakater.com/reload: *secret
