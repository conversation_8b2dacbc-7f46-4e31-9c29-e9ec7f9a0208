---
# yaml-language-server: $schema=https://kubernetes-schemas.pages.dev/source.toolkit.fluxcd.io/ocirepository_v1.json
apiVersion: source.toolkit.fluxcd.io/v1
kind: OCIRepository
metadata:
  name: rook-ceph
spec:
  interval: 5m
  layerSelector:
    mediaType: application/vnd.cncf.helm.chart.content.v1.tar+gzip
    operation: copy
  ref:
    tag: v1.17.5
  url: oci://ghcr.io/rook/rook-ceph
---
# yaml-language-server: $schema=https://kubernetes-schemas.pages.dev/helm.toolkit.fluxcd.io/helmrelease_v2.json
apiVersion: helm.toolkit.fluxcd.io/v2
kind: HelmRelease
metadata:
  name: rook-ceph-operator
spec:
  interval: 1h
  timeout: 15m
  chartRef:
    kind: OCIRepository
    name: rook-ceph
  install:
    remediation:
      retries: -1
  upgrade:
    cleanupOnFail: true
    remediation:
      retries: 3
  values:
    csi:
      cephFSKernelMountOptions: ms_mode=prefer-crc
      # NOTE: Enable the driver and shapshotter if you want to use CephFS
      enableCephfsDriver: false
      enableCephfsSnapshotter: false
      enableLiveness: true
      serviceMonitor:
        enabled: true
    enableDiscoveryDaemon: true
    image:
      repository: ghcr.io/rook/ceph
    monitoring:
      enabled: true
    resources:
      requests:
        memory: 128Mi # unchangable
        cpu: 100m # unchangable
      limits: {}
