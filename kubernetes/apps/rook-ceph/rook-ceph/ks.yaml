---
# yaml-language-server: $schema=https://kubernetes-schemas.pages.dev/kustomize.toolkit.fluxcd.io/kustomization_v1.json
apiVersion: kustomize.toolkit.fluxcd.io/v1
kind: Kustomization
metadata:
  name: &app rook-ceph
  namespace: &namespace rook-ceph
spec:
  commonMetadata:
    labels:
      app.kubernetes.io/name: *app
  decryption:
    provider: sops
    secretRef:
      name: sops-age
  healthChecks:
    - apiVersion: helm.toolkit.fluxcd.io/v2
      kind: HelmRelease
      name: rook-ceph-operator
      namespace: *namespace
  interval: 1h
  path: ./kubernetes/apps/rook-ceph/rook-ceph/app
  prune: true
  retryInterval: 2m
  sourceRef:
    kind: GitRepository
    name: flux-system
    namespace: flux-system
  targetNamespace: *namespace
  timeout: 5m
---
# yaml-language-server: $schema=https://kubernetes-schemas.pages.dev/kustomize.toolkit.fluxcd.io/kustomization_v1.json
apiVersion: kustomize.toolkit.fluxcd.io/v1
kind: Kustomization
metadata:
  name: &app rook-ceph-cluster
  namespace: &namespace rook-ceph
spec:
  commonMetadata:
    labels:
      app.kubernetes.io/name: *app
  dependsOn:
    - name: rook-ceph
      namespace: *namespace
    - name: volsync
      namespace: volsync-system
  healthChecks:
    - apiVersion: helm.toolkit.fluxcd.io/v2
      kind: HelmRelease
      name: rook-ceph-cluster
      namespace: *namespace
    - apiVersion: ceph.rook.io/v1
      kind: CephCluster
      name: rook-ceph
      namespace: *namespace
  healthCheckExprs:
    - apiVersion: ceph.rook.io/v1
      kind: CephCluster
      failed: status.ceph.health == 'HEALTH_ERR'
      current: status.ceph.health in ['HEALTH_OK', 'HEALTH_WARN']
  interval: 1h
  path: ./kubernetes/apps/rook-ceph/rook-ceph/cluster
  postBuild:
    substitute:
      APP: *app
      GATUS_SUBDOMAIN: rook
  prune: true
  retryInterval: 2m
  sourceRef:
    kind: GitRepository
    name: flux-system
    namespace: flux-system
  targetNamespace: *namespace
  timeout: 15m
