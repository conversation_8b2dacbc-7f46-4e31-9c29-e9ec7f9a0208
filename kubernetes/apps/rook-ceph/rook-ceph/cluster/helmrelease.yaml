---
# yaml-language-server: $schema=https://kubernetes-schemas.pages.dev/source.toolkit.fluxcd.io/ocirepository_v1.json
apiVersion: source.toolkit.fluxcd.io/v1
kind: OCIRepository
metadata:
  name: rook-ceph-cluster
spec:
  interval: 5m
  layerSelector:
    mediaType: application/vnd.cncf.helm.chart.content.v1.tar+gzip
    operation: copy
  ref:
    tag: v1.17.5
  url: oci://ghcr.io/rook/rook-ceph-cluster
---
# yaml-language-server: $schema=https://kubernetes-schemas.pages.dev/helm.toolkit.fluxcd.io/helmrelease_v2.json
apiVersion: helm.toolkit.fluxcd.io/v2
kind: HelmRelease
metadata:
  name: rook-ceph-cluster
spec:
  interval: 1h
  timeout: 15m
  chartRef:
    kind: OCIRepository
    name: rook-ceph-cluster
  install:
    remediation:
      retries: -1
  upgrade:
    cleanupOnFail: true
    remediation:
      retries: 3
  values:
    monitoring:
      enabled: true
      createPrometheusRules: true
    toolbox:
      enabled: true
    cephClusterSpec:
      cephConfig:
        global:
          bdev_enable_discard: "true" # quote
          bdev_async_discard_threads: "1" # quote
          osd_class_update_on_start: "false" # quote
          device_failure_prediction_mode: local # requires mgr module
      cleanupPolicy:
        wipeDevicesFromOtherClusters: true
      crashCollector:
        disable: false
      csi:
        readAffinity:
          enabled: true
      dashboard:
        enabled: true
        urlPrefix: /
        ssl: false
        prometheusEndpoint: http://prometheus-operated.observability.svc.cluster.local:9090
      mgr:
        modules:
          - name: diskprediction_local
            enabled: true
          - name: insights
            enabled: true
          - name: pg_autoscaler
            enabled: true
          - name: rook
            enabled: true
      network:
        provider: multus
        connections:
          requireMsgr2: true
      storage:
        useAllNodes: true
        useAllDevices: false
        devicePathFilter: /dev/disk/by-id/nvme-QEMU_NVMe_Ctrl_incus_disk0.*
        config:
          osdsPerDevice: "1"
    cephBlockPools:
      - name: ceph-blockpool
        spec:
          failureDomain: host
          replicated:
            size: 3
        storageClass:
          enabled: true
          name: ceph-block
          isDefault: true
          reclaimPolicy: Delete
          allowVolumeExpansion: true
          volumeBindingMode: Immediate
          mountOptions: ["discard"]
          parameters:
            compression_mode: aggressive
            compression_algorithm: zstd
            imageFormat: "2"
            imageFeatures: layering,fast-diff,object-map,deep-flatten,exclusive-lock
            csi.storage.k8s.io/provisioner-secret-name: rook-csi-rbd-provisioner
            csi.storage.k8s.io/provisioner-secret-namespace: "{{ .Release.Namespace }}"
            csi.storage.k8s.io/controller-expand-secret-name: rook-csi-rbd-provisioner
            csi.storage.k8s.io/controller-expand-secret-namespace: "{{ .Release.Namespace }}"
            csi.storage.k8s.io/node-stage-secret-name: rook-csi-rbd-node
            csi.storage.k8s.io/node-stage-secret-namespace: "{{ .Release.Namespace }}"
            csi.storage.k8s.io/fstype: ext4
    cephBlockPoolsVolumeSnapshotClass:
      enabled: true
      name: csi-ceph-blockpool
      isDefault: false
      deletionPolicy: Delete
    # NOTE: After disabling the filesystem, the filesystem can be removed with the following commands:
    # ceph fs fail ceph-filesystem && ceph fs rm ceph-filesystem --yes-i-really-mean-it
    cephFileSystems: []
    cephObjectStores: []
