---
# yaml-language-server: $schema=https://kubernetes-schemas.pages.dev/source.toolkit.fluxcd.io/ocirepository_v1.json
apiVersion: source.toolkit.fluxcd.io/v1
kind: OCIRepository
metadata:
  name: kube-prometheus-stack
spec:
  interval: 5m
  layerSelector:
    mediaType: application/vnd.cncf.helm.chart.content.v1.tar+gzip
    operation: copy
  ref:
    tag: 75.7.0
  url: oci://ghcr.io/prometheus-community/charts/kube-prometheus-stack
---
# yaml-language-server: $schema=https://kubernetes-schemas.pages.dev/helm.toolkit.fluxcd.io/helmrelease_v2.json
apiVersion: helm.toolkit.fluxcd.io/v2
kind: HelmRelease
metadata:
  name: kube-prometheus-stack
spec:
  interval: 1h
  chartRef:
    kind: OCIRepository
    name: kube-prometheus-stack
  install:
    remediation:
      retries: -1
  upgrade:
    cleanupOnFail: true
    remediation:
      retries: 3
  values:
    crds:
      enabled: true
      upgradeJob:
        enabled: true
        forceConflicts: true
    cleanPrometheusOperatorObjectNames: true
    alertmanager:
      route:
        main:
          enabled: true
          hostnames: ["alertmanager.achva.casa"]
          parentRefs:
            - name: internal
              namespace: kube-system
              sectionName: https
      alertmanagerSpec:
        alertmanagerConfiguration:
          name: alertmanager
          global:
            resolveTimeout: 5m
        externalUrl: https://alertmanager.achva.casa
        storage:
          volumeClaimTemplate:
            spec:
              storageClassName: ceph-block
              resources:
                requests:
                  storage: 1Gi
    kubeEtcd:
      service:
        selector:
          component: kube-apiserver # etcd runs on control plane nodes
    kubeProxy:
      enabled: false
    prometheus:
      route:
        main:
          enabled: true
          hostnames: ["prometheus.achva.casa"]
          parentRefs:
            - name: internal
              namespace: kube-system
              sectionName: https
      prometheusSpec:
        image:
          registry: docker.io
          repository: prompp/prompp
          tag: 2.53.2-0.2.6
        securityContext:
          runAsNonRoot: true
          runAsUser: 64535
          runAsGroup: 64535
          fsGroup: 64535
        podMonitorSelectorNilUsesHelmValues: false
        probeSelectorNilUsesHelmValues: false
        ruleSelectorNilUsesHelmValues: false
        scrapeConfigSelectorNilUsesHelmValues: false
        serviceMonitorSelectorNilUsesHelmValues: false
        retention: 14d
        retentionSize: 100GB
        resources:
          requests:
            cpu: 100m
          limits:
            memory: 2000Mi
        storageSpec:
          volumeClaimTemplate:
            spec:
              storageClassName: ceph-block
              resources:
                requests:
                  storage: 50Gi
    prometheus-node-exporter:
      fullnameOverride: node-exporter
      prometheus:
        monitor:
          enabled: true
          relabelings:
            - action: replace
              regex: (.*)
              replacement: $1
              sourceLabels: ["__meta_kubernetes_pod_node_name"]
              targetLabel: kubernetes_node
    kube-state-metrics:
      fullnameOverride: kube-state-metrics
      metricLabelsAllowlist:
        - pods=[*]
        - deployments=[*]
        - persistentvolumeclaims=[*]
      prometheus:
        monitor:
          enabled: true
          relabelings:
            - action: replace
              regex: (.*)
              replacement: $1
              sourceLabels: ["__meta_kubernetes_pod_node_name"]
              targetLabel: kubernetes_node
    grafana:
      enabled: false
      forceDeployDashboards: true
    additionalPrometheusRulesMap:
      dockerhub-rules:
        groups:
          - name: dockerhub
            rules:
              - alert: DockerhubRateLimitRisk
                annotations:
                  summary: Kubernetes cluster Dockerhub rate limit risk
                expr: count(time() - container_last_seen{image=~"(docker.io).*",container!=""} < 30) > 100
                labels:
                  severity: critical
      oom-rules:
        groups:
          - name: oom
            rules:
              - alert: OomKilled
                annotations:
                  summary: Container {{ $labels.container }} in pod {{ $labels.namespace }}/{{ $labels.pod }} has been OOMKilled {{ $value }} times in the last 10 minutes.
                expr: (kube_pod_container_status_restarts_total - kube_pod_container_status_restarts_total offset 10m >= 1) and ignoring (reason) min_over_time(kube_pod_container_status_last_terminated_reason{reason="OOMKilled"}[10m]) == 1
                labels:
                  severity: critical
      zfs-rules:
        groups:
          - name: zfs
            rules:
              - alert: ZfsUnexpectedPoolState
                annotations:
                  summary: ZFS pool {{$labels.zpool}} on {{$labels.instance}} is in a unexpected state {{$labels.state}}
                expr: node_zfs_zpool_state{state!="online"} > 0
                labels:
                  severity: critical
