---
# yaml-language-server: $schema=https://kubernetes-schemas.pages.dev/helm.toolkit.fluxcd.io/helmrelease_v2.json
apiVersion: helm.toolkit.fluxcd.io/v2
kind: HelmRelease
metadata:
  name: democratic-csi-nfs
spec:
  interval: 1h
  chart:
    spec:
      chart: democratic-csi
      version: 0.15.0
      sourceRef:
        kind: HelmRepository
        name: democratic-csi
        namespace: flux-system
  install:
    disableHooks: true
    remediation:
      retries: -1
  upgrade:
    cleanupOnFail: true
    disableHooks: true
    remediation:
      retries: 3
  values:
    fullnameOverride: democratic-csi-nfs
    csiDriver:
      # should be globally unique for a given cluster
      name: "org.democratic-csi.nfs"

    # add note here about volume expansion requirements
    storageClasses:
      - name: truenas-nfs
        defaultClass: false
        reclaimPolicy: Delete
        volumeBindingMode: Immediate
        allowVolumeExpansion: true
        parameters:
          # for block-based storage can be ext3, ext4, xfs
          # for nfs should be nfs
          fsType: nfs

          # if true, volumes created from other snapshots will be
          # zfs send/received instead of zfs cloned
          # detachedVolumesFromSnapshots: "false"

          # if true, volumes created from other volumes will be
          # zfs send/received instead of zfs cloned
          # detachedVolumesFromVolumes: "false"
        mountOptions:
          - nfsvers=4.2
          - nconnect=8
          - hard
          - noatime
          - nodiratime
        secrets:
          provisioner-secret:
          controller-publish-secret:
          node-stage-secret:
          node-publish-secret:
          controller-expand-secret:

    volumeSnapshotClasses:
      - name: truenas-nfs
        driver: org.democratic-csi.nfs
        deletionPolicy: Delete
        parameters:
          detachedSnapshots: "false"

    driver:
      image:
        tag: next
      config:
        driver: freenas-api-nfs
        instance_id: truenas-nfs
        httpConnection:
          protocol: https
          host: **************  # Replace with your TrueNAS IP
          port: 443
          # username:
          #   secret: truenas-secret
          #   key: username
          # password:
          #   secret: truenas-secret
          #   key: password
          apiKey:
            secret: truenas-secret
            key: api-key
          allowInsecure: true
        zfs:
          datasetParentName: flashstor/Talos-NFS-Vols  # Replace with your dataset path
          detachedSnapshotsDatasetParentName: flashstor/Talos-NFS-Snaps
          datasetEnableQuotas: true
          datasetEnableReservation: false
          datasetPermissionsMode: "0777"
          datasetPermissionsUser: 0
          datasetPermissionsGroup: 0

        nfs:
          shareHost: **************  # Replace with your TrueNAS IP
          shareAlldirs: false
          shareAllowedHosts: []
          shareAllowedNetworks: []
          shareMaprootUser: root
          shareMaprootGroup: root
          shareMapallUser: ""
          shareMapallGroup: ""

    node:
      driver:
        extraEnv:
          - name: ENABLE_VFS_CACHE_PRESSURE
            value: "1"

      cleanup:
        enabled: true

    controller:
      enabled: true
      strategy: deployment
      replicas: 1
      driver:
        enabled: true
        image:
          tag: next