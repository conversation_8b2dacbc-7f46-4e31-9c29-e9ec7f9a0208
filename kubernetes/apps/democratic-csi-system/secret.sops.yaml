# yaml-language-server: $schema=https://kubernetesjsonschema.dev/v1.18.1-standalone-strict/secret-v1.json
apiVersion: v1
kind: Secret
metadata:
  name: truenas-secret
  namespace: democratic-csi-system
stringData:
  api-key: ENC[AES256_GCM,data:zCrKAeOelzLemU4zsa9ioD5NMecGO1KX1UfMuFJcv4gV/suLYu4tcWQKPrQwfnOSGuk0poLN9wLjc2BaCwnpsBDd,iv:rnHL1HbhuDxk822Spy5BifHLigaPP0KAMMfn6qT+tw0=,tag:rNwjBdrPukAZ63VzVKMcPw==,type:str]
  username: E<PERSON>[AES256_GCM,data:ts6/3nYh1Tg=,iv:dkD+7jvWoYLlWrCDcbnRwjEyi8Y+LegAqNXPc9Vlsn8=,tag:/CmiWyytxqcLooeeW81N9w==,type:str]
  password: ENC[AES256_GCM,data:0DLIHxcVqbZ08o4zTv91Ld/VgODg09mVIjsv5w==,iv:LCEFaayNRKequjeJ55OBq9muABkcwEF8lAdbgZdMtKU=,tag:8qECjcbKj8O+QRuRK36sxg==,type:str]
  ssh-private-key: ENC[AES256_GCM,data:cH6Pt5F+cqlgg+CpYX0v0HJwtLKJDNKfnH/7/55T/6MUFavxoo6vrpPeTk7d0Ky+Gi7yb5AwLEuiwYGQ1RMzNgw0i9UqvSns6M2ABE4SAy4RmyOL5g46y6OlVwFlqSN7MGrtTToDLyy68Fvj7fjS7t8dRuwlxoZGA5Q9eSFctGkHUOYkegWFi1G2VSC8hgYr7k9IyyAtOaSAJ3+cbFddQyIFhQUQD/gkcDGrYEwXw2CvOksi5XN4RPj8b3242dxEMCW2hXyoia9H2Pq0JzY5rcnGVUO5GwToqk+TXiWI74WieLoyCfRBGBjmRtpsJ5JuY+R0Scmuw6VEnmotO6HJ1Oo+x2TKlc5uz0ghCn8Ir+CLhW2czEQikcX29sWdgGzzQ9H9wA5Vpz6Nmsr3LHHNcxd6KH1eHDh5TuqLEUyYEKdWehnsoRiZW3QS1kPXCa/cyV1T/pBAaPY4DBfdMMgg03NvgMPEXWWg6UWb2j1W9tSBFBld+WWR/8fHgoDTv4Je+iLe39bR6VUf3RgtHNd/i27nWKn6MEDYLznFaeZEgpIZjpa5YG2H+dZKXJWjAEi7VaGn+r7ECQj23En/E7CjfTRunW4XaI0tCGGH7pzkcxbR2TJaDMhEtf7tthXJQ4CWwGT7DxUQdWWC3VZ8FANWvLqQ7h8gK2ZQXjVxbgj9yisfow+lxqHB7GKrMfV+Ug2TnN/efmxRdrY5BjhPgUr77JEx8C9FAKs9+TKYvt+U2siPeo5NhAFKRHMLjDjzBUkakHXiqc0ZKphzpwblgrp2Byty7RPoobUYt9aq4g48u53hBtpzs2rUxHq/5JtRwA+q6YD5spAvv2FbPeh8mhk0tXQuf3vUSeU1xhjvOMMoUwvUeOLKPHWMcPItGOVm3WQ3EuJhEFFaktdXl8o3xBt/StJpiac/k5FkFXG0snmHCP635ULD0iM0qZQ1j62DOl3dfYMifDFcf9JIw2pLgAR2SC4Yj/6Jkd6KTfPDPIZk9nN1k7KAjeUmxTnIzpiX/tFzI7/FtmSh3WdEz3d9ZoAWGU4i+F51NWwNWtJtDcGMVghwjSe6fB832w+3nn2EIdhogPmNkWjN3DBZaRmeK8y3hlQKSmbet5qsr1xXd1hECer3G6An3RELBJY/VkB+afHH2YvLMZ3G+q+8T2psV31QDSt+VYra08wnyQDIVBQIZJcLgKnDrT+8FyZPYpOgPDW+yX4Kplrv7Y9lfftbSxNDCqzBzL1hycBUK/UwXuWYqAEjcdBwgSDKiqr8Mi8EJxjZyiXK12C/EWyEPJYEOyvJwnHNA5FO3S2VgNR9P0p5OqY8eSnkvb6xZcOkrjaRI8y+kbYE+SvJYKdmt5AgPg2gz0Fr99xjibfeofOdHDu7UAdhL8o+Aly9haErkQOCTxc+oBcYUZfVs/1BSZT2/ugsq3XVLE7snC8Bz/h2G+Q4os5CxXuwVeEMVngreSFj7eZk1sAcDFVTWjpXa7AzqhDA0baIugG9fALzIJUGMdolwrZzKEfgDpheDZIbDpe3ob2mppVNAypx5F4bnk4SIpvkt29gx6DZud+UqeDEN39s19Yb5TXCKbqgvb6bBGsM2AMdAF8+LJDNp4kz4hZKqX/IQWFPcuIlexZ8bzh9gh5LTvJdm4SmIfmIW6kGGun+JiDZ5dZ5u4K0iZNvwhPWrwbQqo66Cvk5V6oZ5QmPuax8smzF9Ad1CVs06/CznPTuQgKlGZCt6GJ/cDoKBhFqD7vvL0rAVK44PfWxmI6oIE3Ihyg6kG1pimdtoZ1akxZksObD+GC4Um1IbA+UAndNTL6IFw7QHn9+HAX+5oPSup2a1nOHsPg082ybIPiMsk5KKHVi/wuQdFSF3vJBAJFApWBlxHx0Z6hTBgVic2LXHVRO9DEd60yJ7isVOO3T7fdYCAPKaXW9mM6N7nDlzz4wrjD6UxBYAAWKSU4hM2LdX6h9lJrVVubd74xG8RjMaeCuZftgIMJhZcpqzfV7NaHg7KZthk5olIGLhcxvjqekMFq2fR/DUyDfyRjQIAqzkP/9c00IjOl3e5A+ecbWPfbKo9RVAoKmNFsoepaiamXY7cHEQVMH5A0G8J6f+RlBFhfbyOx6I2qY/WjVbSfbA3xhTAFfCqY6fRY/wf0EDNWajZZ2Y0I40ZDfxSe9WNSW6Vp0oBJfIOHgvpcj9i0ORW5Ue9gVH0P+svLTKVptAfo5D7MimUtUcTbmKfzIBGWNLoSUB9GtivMVFsBq4FpImKp/TkRdmYN6qyy0ZK0X5ZjupWOGvYW1bHnuy6/Izk3bXOJcL4QOTb022XDakN1+rUX1sACx5d6Fo5BIFRcOQY2FcCHCvcLF0wJhcTNnTSg/4n+T/Q==,iv:29jSv5zdc7sxxW5sZWWojbA5xbXp9TfJdz8mBh/0fR4=,tag:dUfZGoF4M2A72UK6S1FmCA==,type:str]
sops:
  age:
    - recipient: age14dyfyrt956ham3g0rewmdjau22e65pkunc4284gnt5mdd68macdsgk7jld
      enc: |
        -----BEGIN AGE ENCRYPTED FILE-----
        YWdlLWVuY3J5cHRpb24ub3JnL3YxCi0+IFgyNTUxOSA3MlErSDVBY3lndEFGMFhk
        Rnc4azE5VFFFVWFibDYvVmZvVGpXOXA4MWhRCnhkUlV0eXFxNk1mWmxEYlhaT2tH
        ZDVTb2pkV2NJYkVDdkdoZ3hpOUxZUXMKLS0tIGs5ZmFMU1dKZmdNTDhnMTE0aHlL
        RWNvVEdxSlNSL0xQdkNWM1lSc04zTk0K6ls0TCEuDOvgRn5rSeDbzL5y/9WGUv6u
        Pg6str5e+iprGHZli1ZC3t3wU+JV/4toSU1Fy40PdLqQJIA86+vYUA==
        -----END AGE ENCRYPTED FILE-----
  lastmodified: "2025-07-17T13:36:09Z"
  mac: ENC[AES256_GCM,data:K/3PUTxbB4g4oAWNwIrk/MM/ANxCPt1jEqJqnitykBlBT3RexWpBCPte8l1dtrGz2LjLm8BBRxuPjraxXSSbg2wXDpq7dbHpAB1hHQo6eP8tZ971WY44VfmivB99l913krih7APv9u0ktDqR3adQOhETXzH8q9gHuJvjQTCQla0=,iv:JAR3ElVum0bzliIDLy02B+NYeIFR/VVfSVKjCP18FAI=,tag:kdXLUSMk0f07gQPDXrY83Q==,type:str]
  encrypted_regex: ^(data|stringData)$
  mac_only_encrypted: true
  version: 3.10.2
