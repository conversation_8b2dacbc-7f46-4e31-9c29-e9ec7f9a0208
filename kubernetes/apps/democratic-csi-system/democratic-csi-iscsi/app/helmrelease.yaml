---
# yaml-language-server: $schema=https://kubernetes-schemas.pages.dev/helm.toolkit.fluxcd.io/helmrelease_v2.json
apiVersion: helm.toolkit.fluxcd.io/v2
kind: HelmRelease
metadata:
  name: democratic-csi-iscsi
spec:
  interval: 1h
  chart:
    spec:
      chart: democratic-csi
      version: 0.15.0
      sourceRef:
        kind: HelmRepository
        name: democratic-csi
        namespace: flux-system
  install:
    disableHooks: true
    remediation:
      retries: -1
  upgrade:
    cleanupOnFail: true
    disableHooks: true
    remediation:
      retries: 3
  values:
    fullnameOverride: democratic-csi-iscsi
    csiDriver:
      name: "org.democratic-csi.iscsi"

    storageClasses:
      - name: truenas-iscsi
        defaultClass: true
        reclaimPolicy: Delete
        volumeBindingMode: Immediate
        allowVolumeExpansion: true
        parameters:
          fsType: ext4
        mountOptions: []
        secrets:
          provisioner-secret:
          controller-publish-secret:
          node-stage-secret:
          node-publish-secret:
          controller-expand-secret:

    volumeSnapshotClasses:
      - name: truenas-iscsi
        driver: org.democratic-csi.iscsi
        annotations:
          snapshot.storage.kubernetes.io/is-default-class: "true"
        deletionPolicy: Delete
        parameters:
          detachedSnapshots: "false"

    driver:
      image:
        tag: next
      config:
        driver: freenas-api-iscsi
        instance_id: truenas-iscsi
        httpConnection:
          protocol: https
          host: **************  # Replace with your TrueNAS IP
          port: 443
          # username:
          #   secret: truenas-secret
          #   key: username
          # password:
          #   secret: truenas-secret
          #   key: password
          apiKey:
            secret: truenas-secret
            key: api-key
          allowInsecure: true
        zfs:
          cli:
            sudoEnabled: true
          # Total volume name (zvol/<datasetParentName>/<pvc name>) length cannot exceed 63 chars
          # Standard volume naming overhead is 46 chars
          # datasetParentName should therefore be 17 chars or less
          datasetParentName: flashstor/Talos-iSCSI-Vols  # Replace with your dataset path
          detachedSnapshotsDatasetParentName: flashstor/Talos-iSCSI-Snaps
          zvolCompression:
          zvolDedup:
          zvolEnableReservation: false
          zvolBlocksize:

        iscsi:
          targetPortal: [**************:3260]  # Replace with your TrueNAS IP
          targetPortals: []
          interface: ""
          namePrefix: prod-
          nameSuffix: ""
          targetGroups:
            # Get the correct ID from the "portal" section in the TrueNAS UI
            - targetGroupPortalGroup: 1
              # Get the correct ID from the "initiators" section in the TrueNAS UI
              targetGroupInitiatorGroup: 1
              # None, CHAP, or CHAP Mutual
              targetGroupAuthType: None
              # Get the correct ID from the "Authorized Access" section of the UI
              # Only required if using CHAP
              targetGroupAuthGroup:

          extentInsecureTpc: true
          extentXenCompat: false
          extentDisablePhysicalBlocksize: true
          extentBlocksize: 4096
          extentRpm: "SSD"
          extentAvailThreshold: 0

    node:
      driver:
        image:
          registry: docker.io/democraticcsi/democratic-csi
          tag: next
          pullPolicy: Always

      cleanup:
        enabled: true

    controller:
      driver:
        image:
          registry: docker.io/democraticcsi/democratic-csi
          tag: next
          pullPolicy: Always
      replicaCount: 1