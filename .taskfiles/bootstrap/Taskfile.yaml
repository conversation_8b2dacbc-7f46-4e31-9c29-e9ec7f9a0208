---
# yaml-language-server: $schema=https://taskfile.dev/schema.json
version: '3'

tasks:

  talos:
    desc: Bootstrap the Talos cluster
    dir: '{{.TALOS_DIR}}'
    cmds:
      - '[ -f talsecret.sops.yaml ] || talhelper gensecret | sops --filename-override talos/talsecret.sops.yaml --encrypt /dev/stdin > talsecret.sops.yaml'
      - talhelper genconfig
      - talhelper gencommand apply --extra-flags="--insecure" | bash
      - until talhelper gencommand bootstrap | bash; do sleep 10; done
      - until talhelper gencommand kubeconfig --extra-flags="{{.ROOT_DIR}} --force" | bash; do sleep 10; done
    preconditions:
      - test -f {{.ROOT_DIR}}/.sops.yaml
      - test -f {{.SOPS_AGE_KEY_FILE}}
      - test -f {{.TALOS_DIR}}/talconfig.yaml
      - which talhelper talosctl sops

  apps:
    desc: Bootstrap apps into the Talos cluster
    cmd: bash {{.SCRIPTS_DIR}}/bootstrap-apps.sh
    preconditions:
      - msg: Unsupported bash version, run `brew install bash` to upgrade
        sh: '{{if eq OS "darwin"}}test -f /opt/homebrew/bin/bash || test -f /usr/local/bin/bash{{end}}'
      - test -f {{.KUBECONFIG}}
      - test -f {{.ROOT_DIR}}/.sops.yaml
      - test -f {{.SCRIPTS_DIR}}/bootstrap-apps.sh
      - test -f {{.SOPS_AGE_KEY_FILE}}
