---
# yaml-language-server: $schema=https://taskfile.dev/schema.json
version: '3'

tasks:

  generate-config:
    desc: Generate Talos configuration
    dir: '{{.TALOS_DIR}}'
    cmd: talhelper genconfig
    preconditions:
      - test -f {{.TALOS_DIR}}/talconfig.yaml
      - test -f {{.ROOT_DIR}}/.sops.yaml
      - test -f {{.SOPS_AGE_KEY_FILE}}
      - which talhelper

  apply-node:
    desc: Apply <PERSON><PERSON> config to a node [IP=required]
    dir: '{{.TALOS_DIR}}'
    cmd: talhelper gencommand apply --node {{.IP}} --extra-flags '--mode={{.MODE}}' | bash
    vars:
      MODE: '{{.MODE | default "auto"}}'
    requires:
      vars: [IP]
    preconditions:
      - talosctl --nodes {{.IP}} get machineconfig
      - talosctl config info
      - test -f {{.TALOSCONFIG}}
      - which talhelper talosctl yq

  upgrade-node:
    desc: Upgrade Talos on a single node [IP=required]
    dir: '{{.TALOS_DIR}}'
    cmd: talhelper gencommand upgrade --node {{.IP}} --extra-flags "--image='{{.TALOS_IMAGE}}:{{.TALOS_VERSION}}' --timeout=10m" | bash
    vars:
      TALOS_IMAGE:
        sh: yq '.nodes[] | select(.ipAddress == "{{.IP}}") | .talosImageURL' {{.TALOS_DIR}}/talconfig.yaml
      TALOS_VERSION:
        sh: yq '.talosVersion' {{.TALOS_DIR}}/talenv.yaml
    requires:
      vars: [IP]
    preconditions:
      - talosctl --nodes {{.IP}} get machineconfig
      - talosctl config info
      - test -f {{.TALOSCONFIG}}
      - which kubectl talhelper talosctl yq

  upgrade-k8s:
    desc: Upgrade Kubernetes
    dir: '{{.TALOS_DIR}}'
    cmd: talhelper gencommand upgrade-k8s --extra-flags "--to '{{.KUBERNETES_VERSION}}'" | bash
    vars:
      KUBERNETES_VERSION:
        sh: yq '.kubernetesVersion' {{.TALOS_DIR}}/talenv.yaml
    preconditions:
      - talosctl config info
      - test -f {{.TALOSCONFIG}}
      - which talhelper talosctl yq

  reset:
    desc: Resets nodes back to maintenance mode
    dir: '{{.TALOS_DIR}}'
    prompt: This will destroy your cluster and reset the nodes back to maintenance mode... continue?
    cmd: talhelper gencommand reset --extra-flags="--reboot {{- if eq .CLI_FORCE false }} --system-labels-to-wipe STATE --system-labels-to-wipe EPHEMERAL{{ end }} --graceful=false --wait=false" | bash
    preconditions:
      - which talhelper
