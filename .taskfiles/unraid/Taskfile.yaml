---
# yaml-language-server: $schema=https://taskfile.dev/schema.json
version: '3'

vars:
  CLUSTER_CONFIG: '{{.ROOT_DIR}}/cluster.yaml'
  NODES_CONFIG: '{{.ROOT_DIR}}/nodes.yaml'
  TALOS_VERSION: "v1.10.4"
  TALOS_SCHEMATIC_ID: "95e4d9f2ace4d3fddb8d147e4a40874d37e26972e6e6170c9140f125cac8de35"
  TALOS_ISO_URL: "https://factory.talos.dev/image/{{.TALOS_SCHEMATIC_ID}}/{{.TALOS_VERSION}}/metal-amd64-secureboot.iso"

tasks:
  provision:
    desc: Provision VMs on Unraid for Talos cluster
    cmds:
      - task: download-talos-iso
      - task: create-vm-disks
      - task: generate-vm-configs
      - task: create-vms
      - task: start-vms

  download-talos-iso:
    desc: Download Talos Linux ISO to Unraid
    cmds:
      - |
        ssh -o StrictHostKeyChecking=no root@{{.UNRAID_HOST}} "
        if [ ! -f {{.UNRAID_ISO_PATH}}/talos-amd64.iso ]; then
          wget -O {{.UNRAID_ISO_PATH}}/talos-amd64.iso {{.TALOS_ISO_URL}}
        fi"
    vars:
      UNRAID_HOST:
        sh: yq eval '.unraid_host' {{.CLUSTER_CONFIG}}
      UNRAID_ISO_PATH:
        sh: yq eval '.unraid_iso_path' {{.CLUSTER_CONFIG}}

  create-vm-disks:
    desc: Create VM disk images on Unraid
    cmds:
      - |
        for node in $(yq eval '.nodes[].name' {{.NODES_CONFIG}}); do
          ssh -o StrictHostKeyChecking=no root@{{.UNRAID_HOST}} "
          mkdir -p {{.UNRAID_VM_STORAGE_PATH}}/$node
          if [ ! -f {{.UNRAID_VM_STORAGE_PATH}}/$node/system.img ]; then
            qemu-img create -f qcow2 {{.UNRAID_VM_STORAGE_PATH}}/$node/system.img 500G
          fi
          if [ ! -f {{.UNRAID_VM_STORAGE_PATH}}/$node/storage.img ]; then
            qemu-img create -f qcow2 {{.UNRAID_VM_STORAGE_PATH}}/$node/storage.img 1000G
          fi"
        done
    vars:
      UNRAID_HOST:
        sh: yq eval '.unraid_host' {{.CLUSTER_CONFIG}}
      UNRAID_VM_STORAGE_PATH:
        sh: yq eval '.unraid_vm_storage_path' {{.CLUSTER_CONFIG}}

  generate-vm-configs:
    desc: Generate VM XML configurations
    cmds:
      - |
        for i in $(seq 0 $(($(yq eval '.nodes | length' {{.NODES_CONFIG}}) - 1))); do
          node_name=$(yq eval ".nodes[$i].name" {{.NODES_CONFIG}})
          node_address=$(yq eval ".nodes[$i].address" {{.NODES_CONFIG}})
          node_mac=$(yq eval ".nodes[$i].mac_addr" {{.NODES_CONFIG}})

          cat > /tmp/$node_name.xml << EOF
        <domain type='kvm'>
          <name>$node_name</name>
          <memory unit='KiB'>33554432</memory>
          <currentMemory unit='KiB'>33554432</currentMemory>
          <vcpu placement='static'>8</vcpu>
          <os>
            <type arch='x86_64' machine='pc-q35-9.2'>hvm</type>
            <loader readonly='yes' type='pflash' format='raw'>/usr/share/qemu/ovmf-x64/OVMF_CODE-pure-efi.fd</loader>
            <nvram format='raw'>/etc/libvirt/qemu/nvram/${node_name}_VARS-pure-efi.fd</nvram>
            <bootmenu enable='no'/>
          </os>
          <features>
            <acpi/>
            <apic/>
          </features>
          <cpu mode='host-passthrough' check='none' migratable='on'>
            <topology sockets='1' dies='1' clusters='1' cores='4' threads='2'/>
          </cpu>
          <clock offset='utc'>
            <timer name='hpet' present='no'/>
            <timer name='hypervclock' present='no'/>
            <timer name='pit' tickpolicy='delay'/>
            <timer name='rtc' tickpolicy='catchup'/>
          </clock>
          <on_poweroff>destroy</on_poweroff>
          <on_reboot>restart</on_reboot>
          <on_crash>restart</on_crash>
          <devices>
            <emulator>/usr/local/sbin/qemu</emulator>

            <!-- System disk -->
            <disk type='file' device='disk'>
              <driver name='qemu' type='qcow2' cache='writeback' discard='unmap'/>
              <source file='{{.UNRAID_VM_STORAGE_PATH}}/$node_name/system.img'/>
              <target dev='vda' bus='virtio'/>
              <boot order='2'/>
              <address type='pci' domain='0x0000' bus='0x03' slot='0x00' function='0x0'/>
            </disk>

            <!-- Storage disk -->
            <disk type='file' device='disk'>
              <driver name='qemu' type='qcow2' cache='writeback' discard='unmap'/>
              <source file='{{.UNRAID_VM_STORAGE_PATH}}/$node_name/storage.img'/>
              <target dev='vdb' bus='virtio'/>
              <address type='pci' domain='0x0000' bus='0x04' slot='0x00' function='0x0'/>
            </disk>

            <!-- Talos ISO -->
            <disk type='file' device='cdrom'>
              <driver name='qemu' type='raw'/>
              <source file='{{.UNRAID_ISO_PATH}}/talos-amd64.iso'/>
              <target dev='sda' bus='sata'/>
              <readonly/>
              <boot order='1'/>
              <address type='drive' controller='0' bus='0' target='0' unit='0'/>
            </disk>



            <!-- Controllers -->
            <controller type='pci' index='0' model='pcie-root'/>
            <controller type='pci' index='1' model='pcie-root-port'>
              <target chassis='1' port='0x8'/>
              <address type='pci' domain='0x0000' bus='0x00' slot='0x01' function='0x0'/>
            </controller>
            <controller type='pci' index='2' model='pcie-root-port'>
              <target chassis='2' port='0x9'/>
              <address type='pci' domain='0x0000' bus='0x00' slot='0x01' function='0x1'/>
            </controller>
            <controller type='pci' index='3' model='pcie-root-port'>
              <target chassis='3' port='0xa'/>
              <address type='pci' domain='0x0000' bus='0x00' slot='0x01' function='0x2'/>
            </controller>
            <controller type='pci' index='4' model='pcie-root-port'>
              <target chassis='4' port='0xb'/>
              <address type='pci' domain='0x0000' bus='0x00' slot='0x01' function='0x3'/>
            </controller>
            <controller type='sata' index='0'>
              <address type='pci' domain='0x0000' bus='0x00' slot='0x1f' function='0x2'/>
            </controller>

            <!-- Network -->
            <interface type='direct' trustGuestRxFilters='yes'>
              <mac address='$node_mac'/>
              <source dev='bond0' mode='bridge'/>
              <model type='virtio-net'/>
              <address type='pci' domain='0x0000' bus='0x01' slot='0x00' function='0x0'/>
            </interface>

            <!-- Graphics -->
            <graphics type='vnc' port='-1' autoport='yes' websocket='-1' listen='0.0.0.0'>
              <listen type='address' address='0.0.0.0'/>
            </graphics>
            <video>
              <model type='qxl' ram='65536' vram='16384' vgamem='16384' heads='1' primary='yes'/>
              <address type='pci' domain='0x0000' bus='0x00' slot='0x1e' function='0x0'/>
            </video>

          </devices>
        </domain>
        EOF
        done
    vars:
      UNRAID_HOST:
        sh: yq eval '.unraid_host' {{.CLUSTER_CONFIG}}
      UNRAID_VM_STORAGE_PATH:
        sh: yq eval '.unraid_vm_storage_path' {{.CLUSTER_CONFIG}}
      UNRAID_ISO_PATH:
        sh: yq eval '.unraid_iso_path' {{.CLUSTER_CONFIG}}

  create-vms:
    desc: Create and define VMs in libvirt
    cmds:
      - |
        for node in $(yq eval '.nodes[].name' {{.NODES_CONFIG}}); do
          # Copy NVRAM template
          ssh -o StrictHostKeyChecking=no root@{{.UNRAID_HOST}} "
          cp /usr/share/qemu/ovmf-x64/OVMF_VARS-pure-efi.fd /etc/libvirt/qemu/nvram/${node}_VARS-pure-efi.fd"

          # Copy VM config
          scp -o StrictHostKeyChecking=no /tmp/$node.xml root@{{.UNRAID_HOST}}:/mnt/user/system/libvirt/

          # Define VM
          ssh -o StrictHostKeyChecking=no root@{{.UNRAID_HOST}} "
          virsh define /mnt/user/system/libvirt/$node.xml"
        done
    vars:
      UNRAID_HOST:
        sh: yq eval '.unraid_host' {{.CLUSTER_CONFIG}}

  start-vms:
    desc: Start all VMs
    cmds:
      - |
        for node in $(yq eval '.nodes[].name' {{.NODES_CONFIG}}); do
          ssh -o StrictHostKeyChecking=no root@{{.UNRAID_HOST}} "virsh start $node"
        done
    vars:
      UNRAID_HOST:
        sh: yq eval '.unraid_host' {{.CLUSTER_CONFIG}}

  remove-iso:
    desc: Remove ISO from VMs after Talos installation
    cmds:
      - |
        for i in $(seq 0 $(($(yq eval '.nodes | length' {{.NODES_CONFIG}}) - 1))); do
          node_name=$(yq eval ".nodes[$i].name" {{.NODES_CONFIG}})
          node_address=$(yq eval ".nodes[$i].address" {{.NODES_CONFIG}})
          node_mac=$(yq eval ".nodes[$i].mac_addr" {{.NODES_CONFIG}})

          cat > /tmp/$node_name-no-iso.xml << EOF
        <domain type='kvm'>
          <name>$node_name</name>
          <memory unit='KiB'>33554432</memory>
          <currentMemory unit='KiB'>33554432</currentMemory>
          <vcpu placement='static'>8</vcpu>
          <os>
            <type arch='x86_64' machine='pc-q35-9.2'>hvm</type>
            <loader readonly='yes' type='pflash' format='raw'>/usr/share/qemu/ovmf-x64/OVMF_CODE-pure-efi.fd</loader>
            <nvram format='raw'>/etc/libvirt/qemu/nvram/${node_name}_VARS-pure-efi.fd</nvram>
            <bootmenu enable='no'/>
          </os>
          <features>
            <acpi/>
            <apic/>
          </features>
          <cpu mode='host-passthrough' check='none' migratable='on'>
            <topology sockets='1' dies='1' clusters='1' cores='4' threads='2'/>
          </cpu>
          <clock offset='utc'>
            <timer name='hpet' present='no'/>
            <timer name='hypervclock' present='no'/>
            <timer name='pit' tickpolicy='delay'/>
            <timer name='rtc' tickpolicy='catchup'/>
          </clock>
          <on_poweroff>destroy</on_poweroff>
          <on_reboot>restart</on_reboot>
          <on_crash>restart</on_crash>
          <devices>
            <emulator>/usr/local/sbin/qemu</emulator>

            <!-- System disk -->
            <disk type='file' device='disk'>
              <driver name='qemu' type='qcow2' cache='writeback' discard='unmap'/>
              <source file='{{.UNRAID_VM_STORAGE_PATH}}/$node_name/system.img'/>
              <target dev='vda' bus='virtio'/>
              <boot order='1'/>
              <address type='pci' domain='0x0000' bus='0x03' slot='0x00' function='0x0'/>
            </disk>

            <!-- Storage disk -->
            <disk type='file' device='disk'>
              <driver name='qemu' type='qcow2' cache='writeback' discard='unmap'/>
              <source file='{{.UNRAID_VM_STORAGE_PATH}}/$node_name/storage.img'/>
              <target dev='vdb' bus='virtio'/>
              <address type='pci' domain='0x0000' bus='0x04' slot='0x00' function='0x0'/>
            </disk>

            <!-- Controllers -->
            <controller type='pci' index='0' model='pcie-root'/>
            <controller type='pci' index='1' model='pcie-root-port'>
              <target chassis='1' port='0x8'/>
              <address type='pci' domain='0x0000' bus='0x00' slot='0x01' function='0x0'/>
            </controller>
            <controller type='pci' index='2' model='pcie-root-port'>
              <target chassis='2' port='0x9'/>
              <address type='pci' domain='0x0000' bus='0x00' slot='0x01' function='0x1'/>
            </controller>
            <controller type='pci' index='3' model='pcie-root-port'>
              <target chassis='3' port='0xa'/>
              <address type='pci' domain='0x0000' bus='0x00' slot='0x01' function='0x2'/>
            </controller>
            <controller type='pci' index='4' model='pcie-root-port'>
              <target chassis='4' port='0xb'/>
              <address type='pci' domain='0x0000' bus='0x00' slot='0x01' function='0x3'/>
            </controller>

            <!-- Network -->
            <interface type='direct' trustGuestRxFilters='yes'>
              <mac address='$node_mac'/>
              <source dev='bond0' mode='bridge'/>
              <model type='virtio-net'/>
              <address type='pci' domain='0x0000' bus='0x01' slot='0x00' function='0x0'/>
            </interface>

            <!-- Graphics -->
            <graphics type='vnc' port='-1' autoport='yes' websocket='-1' listen='0.0.0.0'>
              <listen type='address' address='0.0.0.0'/>
            </graphics>
            <video>
              <model type='qxl' ram='65536' vram='16384' vgamem='16384' heads='1' primary='yes'/>
              <address type='pci' domain='0x0000' bus='0x00' slot='0x1e' function='0x0'/>
            </video>

          </devices>
        </domain>
        EOF
        done
      - |
        for node in $(yq eval '.nodes[].name' {{.NODES_CONFIG}}); do
          # Stop VM
          ssh -o StrictHostKeyChecking=no root@{{.UNRAID_HOST}} "virsh destroy $node || true"

          # Undefine VM with NVRAM
          ssh -o StrictHostKeyChecking=no root@{{.UNRAID_HOST}} "virsh undefine $node --nvram || true"

          # Copy updated VM config without ISO
          scp -o StrictHostKeyChecking=no /tmp/$node-no-iso.xml root@{{.UNRAID_HOST}}:/mnt/user/system/libvirt/$node.xml

          # Recreate NVRAM file
          ssh -o StrictHostKeyChecking=no root@{{.UNRAID_HOST}} "cp /usr/share/qemu/ovmf-x64/OVMF_VARS-pure-efi.fd /etc/libvirt/qemu/nvram/${node}_VARS-pure-efi.fd"

          # Redefine VM with new config
          ssh -o StrictHostKeyChecking=no root@{{.UNRAID_HOST}} "virsh define /mnt/user/system/libvirt/$node.xml"

          # Start VM
          ssh -o StrictHostKeyChecking=no root@{{.UNRAID_HOST}} "virsh start $node"
        done
    vars:
      UNRAID_HOST:
        sh: yq eval '.unraid_host' {{.CLUSTER_CONFIG}}
      UNRAID_VM_STORAGE_PATH:
        sh: yq eval '.unraid_vm_storage_path' {{.CLUSTER_CONFIG}}

  destroy-vms:
    desc: Destroy all VMs (for cleanup)
    cmds:
      - |
        for node in $(yq eval '.nodes[].name' {{.NODES_CONFIG}}); do
          ssh -o StrictHostKeyChecking=no root@{{.UNRAID_HOST}} "
          virsh destroy $node || true
          virsh undefine $node --nvram || true"
        done
    vars:
      UNRAID_HOST:
        sh: yq eval '.unraid_host' {{.CLUSTER_CONFIG}}
