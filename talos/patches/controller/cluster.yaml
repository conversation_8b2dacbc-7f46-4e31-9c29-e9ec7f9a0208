cluster:
  allowSchedulingOnControlPlanes: true
  apiServer:
    extraArgs:
      # https://kubernetes.io/docs/tasks/extend-kubernetes/configure-aggregation-layer/
      enable-aggregator-routing: true
    disablePodSecurityPolicy: true
  controllerManager:
    extraArgs:
      bind-address: 0.0.0.0
  coreDNS:
    disabled: true
  etcd:
    extraArgs:
      listen-metrics-urls: http://0.0.0.0:2381
    advertisedSubnets:
      - *************/22
  proxy:
    disabled: true
  scheduler:
    extraArgs:
      bind-address: 0.0.0.0
