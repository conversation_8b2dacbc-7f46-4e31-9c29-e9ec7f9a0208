# yaml-language-server: $schema=https://raw.githubusercontent.com/budimanjojo/talhelper/master/pkg/config/schemas/talconfig.json
---
clusterName: home-cluster

talosVersion: "${talosVersion}"
kubernetesVersion: "${kubernetesVersion}"

endpoint: https://***************:6443
additionalApiServerCertSans: &sans
  - "127.0.0.1"
  - "***************"
  - "api.k8s.achva.casa"
additionalMachineCertSans: *sans

clusterPodNets: ["*********/16"]
clusterSvcNets: ["*********/16"]

# Disable built-in CNI to use Cilium
cniConfig:
  name: none

nodes:
  - hostname: "talos-control-node01"
    ipAddress: "***************"
    installDisk: "/dev/nvme0n1"
    machineSpec:
      secureboot: true
    talosImageURL: factory.talos.dev/metal-installer/fd486c1098efa8401d0f68e6036e784bd364a967bcfbfddcffc47a8d3d52485b
    controlPlane: true
    networkInterfaces:
      - interface: "enp5s0"
        dhcp: true
        mtu: 9000
        vip:
          ip: "***************"
  - hostname: "talos-control-node02"
    ipAddress: "***************"
    installDisk: "/dev/nvme0n1"
    machineSpec:
      secureboot: true
    talosImageURL: factory.talos.dev/metal-installer/fd486c1098efa8401d0f68e6036e784bd364a967bcfbfddcffc47a8d3d52485b
    controlPlane: true
    networkInterfaces:
      - interface: "enp5s0"
        dhcp: true
        mtu: 9000
        vip:
          ip: "***************"
  - hostname: "talos-control-node03"
    ipAddress: "***************"
    installDisk: "/dev/nvme0n1"
    machineSpec:
      secureboot: true
    talosImageURL: factory.talos.dev/metal-installer/fd486c1098efa8401d0f68e6036e784bd364a967bcfbfddcffc47a8d3d52485b
    controlPlane: true
    networkInterfaces:
      - interface: "enp5s0"
        dhcp: true
        mtu: 9000
        vip:
          ip: "***************"

# Global patches
patches:
  - "@./patches/global/machine-files.yaml"
  - "@./patches/global/machine-kubelet.yaml"
  - "@./patches/global/machine-network.yaml"
  - "@./patches/global/machine-sysctls.yaml"
  - "@./patches/global/machine-time.yaml"

# Controller patches
controlPlane:
  patches:
    - "@./patches/controller/admission-controller-patch.yaml"
    - "@./patches/controller/cluster.yaml"

